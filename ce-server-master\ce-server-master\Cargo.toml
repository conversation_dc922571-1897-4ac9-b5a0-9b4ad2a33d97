[package]
name = "ce-server"
version = "0.1.0"
authors = ["HoLLy <<EMAIL>>"]
edition = "2018"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
bytes = "1.5"
log = "0.4.11"
miniz_oxide = "0.7.1"
simplelog = "0.12.1"
tokio = { version = "1.33.0", features = ["net", "rt-multi-thread", "io-util"] }
winapi = { version = "0.3.9", features = [
    "handleapi",
    "impl-default",
    "memoryapi",
    "processthreadsapi",
    "tlhelp32",
    "winnt",
], optional = true }


[features]
default = ["windows"]
windows = ["winapi"]
trace = []
